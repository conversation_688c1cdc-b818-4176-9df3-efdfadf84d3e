<template>
  <div class="file-explorer-panel" :class="{ 'maximized': isMaximized, 'normal': !isMaximized, 'collapsed': isCollapsed }">
    <!-- 主容器背景 -->
    <div class="panel-background"></div>

    <!-- 切换按钮 -->
    <button class="toggle-button" @click="toggleCollapse" :title="isCollapsed ? '展开文件管理器' : '收起文件管理器'">
      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path v-if="isCollapsed" d="M9 18L15 12L9 6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        <path v-else d="M15 18L9 12L15 6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
    </button>

    <!-- 展开状态的内容 -->
    <div v-show="!isCollapsed" class="panel-content">
      <!-- 标题栏 -->
      <div class="title-bar">
        <h3 class="panel-title">文件资源管理器</h3>
        <div class="title-actions">
          <button class="action-btn" @click="refreshFiles" title="刷新">
            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M1 4V10H7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M23 20V14H17" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M20.49 9A9 9 0 0 0 5.64 5.64L1 10M23 14L18.36 18.36A9 9 0 0 1 3.51 15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </button>
          <button class="action-btn" @click="createNewFolder" title="新建文件夹">
            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M22 19A2 2 0 0 1 20 21H4A2 2 0 0 1 2 19V5A2 2 0 0 1 4 3H9L11 5H20A2 2 0 0 1 22 7Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <line x1="12" y1="11" x2="12" y2="17" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <line x1="9" y1="14" x2="15" y2="14" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </button>
        </div>
      </div>

      <!-- 分隔线 -->
      <div class="separator-line"></div>

      <!-- 文件树区域 -->
      <div class="file-tree-container" @scroll="handleScroll">
        <div class="file-tree">
          <div 
            v-for="item in fileTree" 
            :key="item.id" 
            class="file-item"
            :class="{ 
              'selected': selectedItem === item.id,
              'folder': item.type === 'folder',
              'file': item.type === 'file'
            }"
            :style="{ paddingLeft: (item.level * 16 + 12) + 'px' }"
            @click="handleItemClick(item)"
            @dblclick="handleItemDoubleClick(item)"
          >
            <!-- 展开/收起图标 -->
            <button 
              v-if="item.type === 'folder'" 
              class="expand-btn"
              @click.stop="toggleFolder(item)"
            >
              <svg width="12" height="12" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path v-if="item.expanded" d="M6 9L12 15L18 9" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path v-else d="M9 18L15 12L9 6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </button>
            <div v-else class="expand-placeholder"></div>

            <!-- 文件/文件夹图标 -->
            <div class="item-icon">
              <svg v-if="item.type === 'folder'" width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M22 19A2 2 0 0 1 20 21H4A2 2 0 0 1 2 19V5A2 2 0 0 1 4 3H9L11 5H20A2 2 0 0 1 22 7Z" stroke="currentColor" stroke-width="1.5" fill="rgba(255, 255, 255, 0.1)"/>
              </svg>
              <svg v-else width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M14 2H6A2 2 0 0 0 4 4V20A2 2 0 0 0 6 22H18A2 2 0 0 0 20 20V8Z" stroke="currentColor" stroke-width="1.5" fill="rgba(255, 255, 255, 0.05)"/>
                <polyline points="14,2 14,8 20,8" stroke="currentColor" stroke-width="1.5"/>
              </svg>
            </div>

            <!-- 文件名 -->
            <span class="item-name">{{ item.name }}</span>
          </div>
        </div>
      </div>

      <!-- 底部状态栏 -->
      <div class="status-bar">
        <span class="file-count">{{ fileCount }} 项</span>
        <span class="current-path">{{ currentPath }}</span>
      </div>
    </div>

    <!-- 自定义滚动条 -->
    <div class="custom-scrollbar" v-show="showScrollbar && !isCollapsed">
      <div
        class="scrollbar-track"
        @click="handleTrackClick"
        ref="scrollbarTrack"
      >
        <div
          class="scrollbar-thumb"
          :style="{
            height: thumbHeight + 'px',
            top: thumbTop + 'px'
          }"
          @mousedown="startDrag"
        ></div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue';
import { getCurrentWindow } from '@tauri-apps/api/window';

// 窗口最大化状态
const isMaximized = ref(false);
const isCollapsed = ref(false);

// 滚动条相关状态
const showScrollbar = ref(false);
const thumbHeight = ref(50);
const thumbTop = ref(0);
const isDragging = ref(false);
const dragStartY = ref(0);
const dragStartScrollTop = ref(0);

// DOM引用
const scrollbarTrack = ref<HTMLElement | null>(null);

// 文件树状态
const selectedItem = ref<string | null>(null);
const currentPath = ref('E:\\Documents\\Projects\\创作项目');

// 文件树数据接口
interface FileItem {
  id: string;
  name: string;
  type: 'file' | 'folder';
  level: number;
  expanded?: boolean;
  children?: FileItem[];
  path: string;
}

// 示例文件树数据
const fileTree = ref<FileItem[]>([
  {
    id: '1',
    name: '创作项目',
    type: 'folder',
    level: 0,
    expanded: true,
    path: 'E:\\Documents\\Projects\\创作项目',
    children: [
      {
        id: '2',
        name: '小说草稿',
        type: 'folder',
        level: 1,
        expanded: false,
        path: 'E:\\Documents\\Projects\\创作项目\\小说草稿'
      },
      {
        id: '3',
        name: '示例文档.txt',
        type: 'file',
        level: 1,
        path: 'E:\\Documents\\Projects\\创作项目\\示例文档.txt'
      },
      {
        id: '4',
        name: '角色设定.md',
        type: 'file',
        level: 1,
        path: 'E:\\Documents\\Projects\\创作项目\\角色设定.md'
      },
      {
        id: '5',
        name: '世界观设定',
        type: 'folder',
        level: 1,
        expanded: false,
        path: 'E:\\Documents\\Projects\\创作项目\\世界观设定'
      }
    ]
  }
]);

// 计算属性
const fileCount = computed(() => {
  let count = 0;
  const countItems = (items: FileItem[]) => {
    items.forEach(item => {
      count++;
      if (item.children && item.expanded) {
        countItems(item.children);
      }
    });
  };
  countItems(fileTree.value);
  return count;
});

// 方法
const toggleCollapse = () => {
  isCollapsed.value = !isCollapsed.value;
  nextTick(() => {
    updateScrollbar();
  });
};

const refreshFiles = () => {
  console.log('刷新文件列表');
  // 这里可以添加刷新文件列表的逻辑
};

const createNewFolder = () => {
  console.log('创建新文件夹');
  // 这里可以添加创建新文件夹的逻辑
};

const handleItemClick = (item: FileItem) => {
  selectedItem.value = item.id;
  currentPath.value = item.path;
};

const handleItemDoubleClick = (item: FileItem) => {
  if (item.type === 'folder') {
    toggleFolder(item);
  } else {
    // 打开文件
    console.log('打开文件:', item.name);
  }
};

const toggleFolder = (item: FileItem) => {
  item.expanded = !item.expanded;
  updateScrollbar();
};

// 滚动条相关方法
const getContentElement = (): HTMLElement | null => {
  return document.querySelector('.file-tree-container');
};

const updateScrollbar = () => {
  nextTick(() => {
    const contentEl = getContentElement();
    if (!contentEl) return;

    const { scrollTop, scrollHeight, clientHeight } = contentEl;
    showScrollbar.value = scrollHeight > clientHeight;

    if (showScrollbar.value && scrollbarTrack.value) {
      const trackHeight = scrollbarTrack.value.clientHeight;
      const thumbHeightRatio = clientHeight / scrollHeight;
      thumbHeight.value = Math.max(trackHeight * thumbHeightRatio, 20);
      thumbHeight.value = Math.min(thumbHeight.value, trackHeight);

      const maxScrollTop = scrollHeight - clientHeight;
      const scrollRatio = maxScrollTop > 0 ? scrollTop / maxScrollTop : 0;
      const maxThumbTop = trackHeight - thumbHeight.value;
      thumbTop.value = Math.max(0, Math.min(scrollRatio * maxThumbTop, maxThumbTop));
    }
  });
};

const handleScroll = () => {
  if (!isDragging.value) {
    updateScrollbar();
  }
};

const handleTrackClick = (event: MouseEvent) => {
  const contentEl = getContentElement();
  if (!contentEl || !scrollbarTrack.value) return;

  const trackRect = scrollbarTrack.value.getBoundingClientRect();
  const clickY = event.clientY - trackRect.top;
  const trackHeight = scrollbarTrack.value.clientHeight;
  const clickRatio = clickY / trackHeight;
  const maxScrollTop = contentEl.scrollHeight - contentEl.clientHeight;

  contentEl.scrollTop = clickRatio * maxScrollTop;
  updateScrollbar();
};

const startDrag = (event: MouseEvent) => {
  const contentEl = getContentElement();
  if (!contentEl) return;

  isDragging.value = true;
  dragStartY.value = event.clientY;
  dragStartScrollTop.value = contentEl.scrollTop;

  document.addEventListener('mousemove', handleDrag);
  document.addEventListener('mouseup', stopDrag);
  event.preventDefault();
};

const handleDrag = (event: MouseEvent) => {
  if (!isDragging.value || !scrollbarTrack.value) return;

  const contentEl = getContentElement();
  if (!contentEl) return;

  const deltaY = event.clientY - dragStartY.value;
  const trackHeight = scrollbarTrack.value.clientHeight;
  const maxScrollTop = contentEl.scrollHeight - contentEl.clientHeight;
  const dragRatio = deltaY / trackHeight;
  const newScrollTop = dragStartScrollTop.value + (dragRatio * maxScrollTop);

  contentEl.scrollTop = Math.max(0, Math.min(newScrollTop, maxScrollTop));
  updateScrollbar();
};

const stopDrag = () => {
  isDragging.value = false;
  document.removeEventListener('mousemove', handleDrag);
  document.removeEventListener('mouseup', stopDrag);
};

// 检查窗口最大化状态
const checkMaximizedState = async () => {
  try {
    const window = getCurrentWindow();
    const wasMaximized = isMaximized.value;
    isMaximized.value = await window.isMaximized();

    if (wasMaximized !== isMaximized.value) {
      setTimeout(() => {
        updateScrollbar();
      }, 100);
    }
  } catch (error) {
    console.error('检查窗口状态失败:', error);
  }
};

onMounted(async () => {
  await checkMaximizedState();

  setTimeout(() => {
    updateScrollbar();
  }, 100);

  const window = getCurrentWindow();
  const unlisten = await window.onResized(() => {
    checkMaximizedState();
    updateScrollbar();
  });

  onUnmounted(() => {
    unlisten();
    stopDrag();
  });
});
</script>

<style scoped>
.file-explorer-panel {
  position: absolute;
  transition: all 0.3s ease-in-out;
  z-index: 5;
}

/* 最大化状态 */
.file-explorer-panel.maximized {
  top: 58px;
  left: 58px; /* 侧边栏右侧 */
  width: 280px;
  height: 977px;
}

.file-explorer-panel.maximized.collapsed {
  width: 40px;
}

/* 普通状态 */
.file-explorer-panel.normal {
  top: 40px;
  left: 40px;
  width: 193px; /* 280px * 0.69 ≈ 193px */
  height: 674px; /* 977px * 0.69 ≈ 674px */
}

.file-explorer-panel.normal.collapsed {
  width: 28px; /* 40px * 0.69 ≈ 28px */
}

/* 主背景容器 */
.panel-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.07) 0%,
    rgba(255, 255, 255, 0.05) 100%);
  border: 1px solid rgba(255, 255, 255, 0.06);
  border-radius: 20px;
  backdrop-filter: blur(80px);
  -webkit-backdrop-filter: blur(80px);
  box-sizing: border-box;
}

/* 切换按钮 */
.toggle-button {
  position: absolute;
  top: 20px;
  right: 8px;
  width: 24px;
  height: 24px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  color: #FFFFFF;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  z-index: 10;
}

.toggle-button:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
}

/* 面板内容 */
.panel-content {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 16px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 标题栏 */
.title-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
  padding-right: 32px; /* 为切换按钮留空间 */
}

.panel-title {
  font-family: Inter, Avenir, Helvetica, Arial, sans-serif;
  font-size: 16px;
  font-weight: 500;
  color: #FFFFFF;
  margin: 0;
}

.title-actions {
  display: flex;
  gap: 6px;
}

.action-btn {
  width: 24px;
  height: 24px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  color: #FFFFFF;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.action-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
}

/* 分隔线 */
.separator-line {
  width: 100%;
  height: 1px;
  background: rgba(255, 255, 255, 0.09);
  margin-bottom: 12px;
}

/* 文件树容器 */
.file-tree-container {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  margin-bottom: 12px;
  padding-right: 8px;
}

.file-tree-container::-webkit-scrollbar {
  display: none;
}

.file-tree-container {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

/* 文件项 */
.file-item {
  display: flex;
  align-items: center;
  height: 28px;
  padding: 4px 8px;
  margin-bottom: 2px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  user-select: none;
}

.file-item:hover {
  background: rgba(255, 255, 255, 0.1);
}

.file-item.selected {
  background: rgba(13, 255, 0, 0.15);
  border: 1px solid rgba(13, 255, 0, 0.3);
}

/* 展开按钮 */
.expand-btn {
  width: 16px;
  height: 16px;
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 4px;
  border-radius: 2px;
  transition: all 0.2s ease;
}

.expand-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  color: #FFFFFF;
}

.expand-placeholder {
  width: 16px;
  height: 16px;
  margin-right: 4px;
}

/* 文件图标 */
.item-icon {
  width: 16px;
  height: 16px;
  margin-right: 8px;
  color: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
}

.file-item.folder .item-icon {
  color: #FFD700;
}

.file-item.file .item-icon {
  color: rgba(255, 255, 255, 0.6);
}

/* 文件名 */
.item-name {
  font-family: Inter, Avenir, Helvetica, Arial, sans-serif;
  font-size: 12px;
  font-weight: 400;
  color: #FFFFFF;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
}

/* 底部状态栏 */
.status-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-top: 1px solid rgba(255, 255, 255, 0.09);
  font-family: Inter, Avenir, Helvetica, Arial, sans-serif;
  font-size: 10px;
  color: rgba(255, 255, 255, 0.7);
}

.file-count {
  font-weight: 500;
}

.current-path {
  max-width: 120px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 自定义滚动条 */
.custom-scrollbar {
  position: absolute;
  top: 80px;
  right: 8px;
  width: 6px;
  bottom: 60px;
  z-index: 10;
}

.scrollbar-track {
  position: relative;
  width: 100%;
  height: 100%;
  background: rgba(138, 138, 138, 0.3);
  border: 1px solid rgba(138, 138, 138, 0.2);
  border-radius: 3px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.scrollbar-track:hover {
  background: rgba(138, 138, 138, 0.4);
}

.scrollbar-thumb {
  position: absolute;
  left: 0;
  width: 100%;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 3px;
  cursor: grab;
  transition: background-color 0.2s ease;
  min-height: 20px;
}

.scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.8);
}

.scrollbar-thumb:active {
  cursor: grabbing;
  background: rgba(13, 255, 0, 0.7);
}

/* 普通状态下的比例调整 */
.file-explorer-panel.normal .panel-title {
  font-size: 14px;
}

.file-explorer-panel.normal .item-name {
  font-size: 11px;
}

.file-explorer-panel.normal .status-bar {
  font-size: 9px;
}

.file-explorer-panel.normal .file-item {
  height: 24px;
}

.file-explorer-panel.normal .toggle-button {
  width: 20px;
  height: 20px;
  top: 16px;
  right: 6px;
}

.file-explorer-panel.normal .action-btn {
  width: 20px;
  height: 20px;
}

.file-explorer-panel.normal .custom-scrollbar {
  width: 4px;
  top: 60px;
  bottom: 50px;
}
</style>
